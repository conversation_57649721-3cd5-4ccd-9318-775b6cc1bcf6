@props([
    'user' => null,
    'targetResource' => null,
    'targetMob' => null,
    'targetBot' => null,
    'target' => null,
    'isStunned' => false,
    'lastAttacker' => null,
    'lastAttackerResources' => null,
    'routePrefix' => 'battle.mines.custom',
])

@php
    // Определяем какое изображение использовать в зависимости от наличия цели
    // Если нет цели (показывается кнопка "Атаковать случайную цель") - используем action-non-target.png
    // Если есть цель - используем action-bg.png
    $currentTargetType = $user->current_target_type;
    $hasActiveTarget = !empty($currentTargetType) && 
                       in_array($currentTargetType, ['resource', 'mob', 'player', 'bot']) && 
                       ($targetResource !== null || $targetMob !== null || $targetBot !== null || $target !== null);
    $backgroundImage = $hasActiveTarget ? 'assets/UI/action-bg.png' : 'assets/UI/action-non-target.png';
@endphp

{{--
    Компонент для отображения действий с выбранной целью в локациях рудника
    Принимает:
    - user: текущий пользователь
    - targetResource: выбранный ресурс (если есть)
    - targetMob: выбранный моб (если есть)
    - target: выбранный игрок (если есть)
    - isStunned: флаг оглушения игрока
    - routePrefix: префикс маршрута для действий
--}}

<div class="relative bg-contain bg-center bg-no-repeat max-w-xs mx-auto"
    style="background-image: url('{{ asset($backgroundImage) }}'); min-height: 120px; background-size: 100% 100%;">
    {{-- Заголовок блока --}}
    <div class="text-center py-4 text-[#e9d5a0] font-bold text-sm relative z-10">
        Действия
    </div>

    <div class="p-4 space-y-2 relative z-10">
        @if ($isStunned)
            <div class="bg-yellow-900/50 text-yellow-400 p-4 rounded-lg text-center my-4">
                ⚡ Вы оглушены и не можете действовать! ⚡
            </div>
        @else
            @if ($user->current_target_type === 'resource' && $targetResource)
                {{-- Информация о выбранном ресурсе --}}
                <div class="space-y-1.5">
                    <div class="flex items-center bg-black/40 p-1.5 rounded-md backdrop-blur-sm">
                        <img src="{{ $targetResource->resource->icon_path }}"
                            alt="{{ $targetResource->resource->name ?? 'Ресурс' }}" 
                            class="w-6 h-6 rounded-md object-cover"
                            onerror="this.src='{{ asset('assets/resources/EtherealOrb.png') }}'">
                        <div class="ml-1.5 flex-1">
                            <div class="text-[#e9d5a0] text-xs font-semibold mb-0.5 flex justify-between">
                                {{ $targetResource->resource->name ?? 'Ресурс' }}
                                <span class="text-[10px] text-[#9c8d69]">
                                    {{ $targetResource->durability ?? 100 }}%
                                </span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-full h-2 bg-[#1a1915] rounded border border-[#46423a] overflow-hidden">
                                    @php
                                        $durabilityPercent = $targetResource->durability ?? 100;
                                        $durabilityColor = $durabilityPercent > 70 ? '#4CAF50' : ($durabilityPercent > 35 ? '#FFC107' : '#F44336');
                                    @endphp
                                    <div class="h-full" style="width: {{ $durabilityPercent }}%; background-color: {{ $durabilityColor }};"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="attackForm" action="{{ route($routePrefix . '.hit-resource', request()->route('slug')) }}" method="POST">
                        @csrf
                        <button id="attackButton" type="submit" {{ $isStunned ? 'disabled' : '' }}
                            class="w-full py-2 bg-gradient-to-b from-[#607d4a] to-[#3a5a2c] border-2 border-[#8bae6f]
                            rounded-md text-white text-sm font-bold shadow-md
                            {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#6b8a53] hover:to-[#44653c] active:from-[#526b3f] active:to-[#2f4a24]' }} transition-all
                            text-center">
                            {{ $isStunned ? 'Оглушен: действие невозможно' : 'Добыть' }}
                        </button>
                    </form>

                    {{-- Кнопка обновления страницы при стане --}}
                    @if ($isStunned)
                        <button
                            onclick="window.location.reload()"
                            class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                            border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                            hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                            text-center">
                            🔄 Обновить страницу
                        </button>
                    @endif
                </div>
            @elseif($user->current_target_type === 'mob' && $targetMob)
                {{-- Информация о выбранном мобе --}}
                <div class="space-y-1.5">
                    <div class="flex items-center bg-black/40 p-1.5 rounded-md backdrop-blur-sm">
                        <img src="{{ asset($targetMob->icon ?? $targetMob->image_path ?? 'assets/mobs/default.png') }}" alt="{{ $targetMob->name }}"
                            class="w-6 h-6 rounded-md">
                        <div class="ml-1.5 flex-1">
                            <div class="text-[#e9d5a0] text-xs font-semibold mb-0.5 flex justify-between">
                                {{ $targetMob->name }}
                                <span class="text-[10px] text-[#9c8d69]">
                                    {{ $targetMob->current_hp ?? $targetMob->hp }}/{{ $targetMob->max_hp ?? $targetMob->hp }}
                                </span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-full h-2 bg-[#1a1915] rounded border border-[#46423a] overflow-hidden">
                                    @php
                                        $hpPercent = (($targetMob->current_hp ?? $targetMob->hp) / ($targetMob->max_hp ?? $targetMob->hp)) * 100;
                                        $hpColor = $hpPercent > 70 ? '#4CAF50' : ($hpPercent > 35 ? '#FFC107' : '#F44336');
                                    @endphp
                                    <div class="h-full" style="width: {{ $hpPercent }}%; background-color: {{ $hpColor }};"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="attackForm" action="{{ route($routePrefix . '.attack-mob', request()->route('slug')) }}" method="POST">
                        @csrf
                        <button id="attackButton" type="submit" {{ $isStunned ? 'disabled' : '' }}
                            class="w-full py-2 bg-gradient-to-b from-[#913838] to-[#762323] border-2 border-[#c07777]
                            rounded-md text-white text-sm font-bold shadow-md
                            {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e]' }} transition-all
                            text-center">
                            {{ $isStunned ? 'Оглушен: действие невозможно' : 'Атаковать' }}
                        </button>
                    </form>

                    {{-- Кнопка обновления страницы при стане --}}
                    @if ($isStunned)
                        <button
                            onclick="window.location.reload()"
                            class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                            border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                            hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                            text-center">
                            🔄 Обновить страницу
                        </button>
                    @endif
                </div>
            @elseif($user->current_target_type === 'bot' && $targetBot)
                {{-- Информация о выбранном боте --}}
                <div class="space-y-1.5">
                    <div class="flex items-center bg-black/40 p-1.5 rounded-md backdrop-blur-sm">
                        @php
                            // Определяем иконку бота на основе расы и класса
                            $botIcon = $targetBot->image_path ?? 'assets/bots/default.png';
                            if ($targetBot->race === 'solarius' && $targetBot->class === 'warrior') {
                                $botIcon = 'assets/bots/sol_warrior.png';
                            } elseif ($targetBot->race === 'solarius' && $targetBot->class === 'mage') {
                                $botIcon = 'assets/bots/sol_mage.png';
                            } elseif ($targetBot->race === 'lunarius' && $targetBot->class === 'warrior') {
                                $botIcon = 'assets/bots/lun_warrior.png';
                            } elseif ($targetBot->race === 'lunarius' && $targetBot->class === 'mage') {
                                $botIcon = 'assets/bots/lun_mage.png';
                            }
                        @endphp
                        <img src="{{ asset($botIcon) }}" alt="{{ $targetBot->name }}" class="w-6 h-6 rounded-md">
                        <div class="ml-1.5 flex-1">
                            <div class="text-[#e9d5a0] text-xs font-semibold mb-0.5 flex justify-between">
                                {{ $targetBot->name }}
                                <span class="text-[10px] text-[#9c8d69]">
                                    {{ $targetBot->hp }}/{{ $targetBot->max_hp ?? $targetBot->hp }}
                                </span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-full h-2 bg-[#1a1915] rounded border border-[#46423a] overflow-hidden">
                                    @php
                                        $hpPercent = ($targetBot->hp / ($targetBot->max_hp ?? $targetBot->hp)) * 100;
                                        $hpColor = $hpPercent > 70 ? '#4CAF50' : ($hpPercent > 35 ? '#FFC107' : '#F44336');
                                    @endphp
                                    <div class="h-full" style="width: {{ $hpPercent }}%; background-color: {{ $hpColor }};"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="attackForm" action="{{ route($routePrefix . '.attack-bot', request()->route('slug')) }}" method="POST">
                        @csrf
                        <button id="attackButton" type="submit" {{ $isStunned ? 'disabled' : '' }}
                            class="w-full py-2 bg-gradient-to-b from-[#8a5e8a] to-[#6a3c6a] border-2 border-[#b579b5]
                            rounded-md text-white text-sm font-bold shadow-md
                            {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#9a6a9a] hover:to-[#7a487a] active:from-[#7a527a] active:to-[#5a305a]' }} transition-all
                            text-center">
                            {{ $isStunned ? 'Оглушен: действие невозможно' : 'Атаковать' }}
                        </button>
                    </form>

                    {{-- Кнопка обновления страницы при стане --}}
                    @if ($isStunned)
                        <button
                            onclick="window.location.reload()"
                            class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                            border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                            hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                            text-center">
                            🔄 Обновить страницу
                        </button>
                    @endif
                </div>
            @elseif($user->current_target_type === 'player' && $target)
                {{-- Информация о выбранном игроке --}}
                <div class="space-y-1.5">
                    <div class="flex items-center bg-black/40 p-1.5 rounded-md backdrop-blur-sm">
                        <div class="w-6 h-6 rounded-md bg-gradient-to-b from-[#913838] to-[#762323] border border-[#c07777] flex items-center justify-center text-white font-bold text-xs">
                            PvP
                        </div>
                        <div class="ml-1.5 flex-1">
                            <div class="text-[#e9d5a0] text-xs font-semibold mb-0.5 flex justify-between">
                                {{ $target->name }}
                                <span class="text-[10px] text-red-400">
                                    @php
                                        // Получаем актуальные ресурсы игрока-цели из Redis
                                        try {
                                            $targetActualResources = $target->profile->getActualResources();
                                            $targetCurrentHp = $targetActualResources['current_hp'];
                                        } catch (\Exception $e) {
                                            // При ошибке используем значения из БД
                                            $targetCurrentHp = $target->profile->current_hp ?? $target->profile->hp;
                                        }
                                    @endphp
                                    ❤️ {{ $targetCurrentHp }}
                                </span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-full h-2 bg-[#1a1915] rounded border border-[#46423a] overflow-hidden">
                                    @php
                                        $targetMaxHp = $target->profile->max_hp;
                                        $hpPercent = ($targetCurrentHp / $targetMaxHp) * 100;
                                        $hpColor = $hpPercent > 70 ? '#4CAF50' : ($hpPercent > 35 ? '#FFC107' : '#F44336');
                                    @endphp
                                    <div class="h-full" style="width: {{ $hpPercent }}%; background-color: {{ $hpColor }};"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="attackForm" action="{{ route($routePrefix . '.attack-player', request()->route('slug')) }}" method="POST">
                        @csrf
                        <button id="attackButton" type="submit" {{ $isStunned ? 'disabled' : '' }}
                            class="w-full py-2 bg-gradient-to-b from-[#913838] to-[#762323] border-2 border-[#c07777]
                            rounded-md text-white text-sm font-bold shadow-md
                            {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e]' }} transition-all
                            text-center">
                            {{ $isStunned ? 'Оглушен: действие невозможно' : 'Атаковать' }}
                        </button>
                    </form>

                    {{-- Кнопка обновления страницы при стане --}}
                    @if ($isStunned)
                        <button
                            onclick="window.location.reload()"
                            class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                            border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                            hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                            text-center">
                            🔄 Обновить страницу
                        </button>
    @endif
                </div>
            @else
                {{-- Нет выбранной цели --}}
                <form action="{{ route($routePrefix . '.attack-any-player', request()->route('slug')) }}" method="POST">
                    @csrf
                    <button type="submit" {{ $isStunned ? 'disabled' : '' }}
                        class="w-full py-2 bg-gradient-to-b from-[#7a6745] to-[#5a4d36] border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md
                        {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b]' }} transition-all
                        text-center">
                        {{ $isStunned ? 'Оглушен: действие невозможно' : 'Бить любого' }}
                    </button>
                </form>

                {{-- Кнопка обновления страницы при стане (когда нет цели) --}}
                @if ($isStunned)
                    <button
                        onclick="window.location.reload()"
                        class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                        border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                        hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                        text-center">
                        🔄 Обновить страницу
                    </button>
                @endif
            @endif

            {{-- Дополнительные кнопки (Ответный удар) --}}
            <div class="pt-1 space-y-1.5">
                @php
                    // Получаем текущего пользователя
                    $currentUser = $user;

                    // Проверяем последнего атакующего с учетом активности и местоположения
                    $validLastAttacker = null;
                    $validLastAttackerResources = null;

                    if ($currentUser->last_attacker_id) {
                        $candidateAttacker = \App\Models\User::where('id', $currentUser->last_attacker_id)
                            ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
                            ->with('profile', 'statistics')
                            ->first();

                        if ($candidateAttacker) {
                            // Используем улучшенную логику проверки локации
                            $locationService = app(\App\Services\battle\UserLocationService::class);
                            $areInSameLocation = $locationService->arePlayersInSameLocation($currentUser, $candidateAttacker);

                            if ($areInSameLocation) {
                                $validLastAttacker = $candidateAttacker;
                                $validLastAttackerResources = $validLastAttacker->profile->getActualResources();
                            }
                        }
                    }

                    // Проверяем, не выбран ли уже атакующий в качестве цели
                    $isAttackerAlreadyTargeted = $validLastAttacker &&
                        $currentUser->current_target_type === 'player' &&
                        $currentUser->current_target_id == $validLastAttacker->id;
                @endphp

                @if($validLastAttacker && $validLastAttackerResources && $validLastAttackerResources['current_hp'] > 0 && !$isAttackerAlreadyTargeted)
                    <form action="{{ route($routePrefix . '.retaliate', request()->route('slug')) }}" method="POST">
                        @csrf
                        <button type="submit" {{ $isStunned ? 'disabled' : '' }}
                            class="w-full py-2 bg-gradient-to-b from-[#913838] to-[#762323]
                                border-2 border-[#c07777] rounded-md text-white text-xs font-semibold shadow-md
                                {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e]' }}
                                transition-all duration-300 text-center group relative overflow-hidden">

                            {{-- Фоновая анимация при наведении --}}
                            <span class="absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b6b40] to-transparent opacity-0 group-hover:opacity-100 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>

                            <div class="flex items-center justify-center relative z-10">
                                {{-- Иконка атаки --}}
                                <span class="mr-1 text-red-300">⚔️</span>

                                {{-- Текст кнопки --}}
                                <span class="font-bold">Бить в ответ</span>

                                {{-- Имя и HP противника в стильном контейнере --}}
                                <div class="ml-2 flex items-center bg-[#6b2c2c] px-2 py-0.5 rounded border border-[#c07777] shadow-inner">
                                    <span class="text-[10px] text-yellow-200 mr-1">{{ $validLastAttacker->name }}</span>

                                    {{-- Мини-полоска HP --}}
                                    <div class="w-14 h-1.5 bg-[#421a1a] rounded-full overflow-hidden flex-shrink-0">
                                        <div class="{{ $validLastAttackerResources['current_hp'] > $validLastAttacker->profile->max_hp * 0.7 ? 'bg-green-600' : ($validLastAttackerResources['current_hp'] > $validLastAttacker->profile->max_hp * 0.3 ? 'bg-yellow-500' : 'bg-red-600') }} h-full"
                                            style="width: {{ ($validLastAttackerResources['current_hp'] / $validLastAttacker->profile->max_hp) * 100 }}%;">
                                        </div>
                                    </div>

                                    {{-- Числовое значение HP --}}
                                    <span class="text-[9px] text-gray-300 ml-1">
                                        {{ $validLastAttackerResources['current_hp'] }}<span class="text-gray-500">/{{ $validLastAttacker->profile->max_hp }}</span>
                                    </span>
                                </div>
                            </div>
                        </button>
                    </form>
                @endif

                {{-- Кнопка обновления страницы при стане (в блоке ответного удара) --}}
                @if ($isStunned)
                    <button
                        onclick="window.location.reload()"
                        class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                        border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                        hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                        text-center">
                        🔄 Обновить страницу
                    </button>
                @endif

                {{-- Кнопка "Сменить цель" --}}
                @if(($targetResource && $targetResource->resource) || ($user->current_target_type === 'mob' && $targetMob) || ($user->current_target_type === 'bot' && $targetBot) || ($user->current_target_type === 'player' && $target))
                    <form action="{{ route($routePrefix . '.change_target', request()->route('slug')) }}" method="POST" onsubmit="return handleChangeTargetSubmit(this)">
                        @csrf
                        <button type="submit" {{ $isStunned ? 'disabled' : '' }}
                            class="w-full py-1.5 bg-gradient-to-b from-[#7a6745] to-[#5a4d36] border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md
                            {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b]' }} transition-all
                            text-center change-target-btn">
                            <span class="button-text">{{ $isStunned ? 'Заблокировано' : 'Сменить цель' }}</span>
                        </button>
                    </form>
                @endif

                {{-- Кнопка обновления страницы при стане --}}
                @if ($isStunned)
                    <button
                        onclick="window.location.reload()"
                        class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                        border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                        hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                        text-center">
                        🔄 Обновить страницу
                    </button>
                @endif
            </div>

        @endif
    </div>
</div>


{{-- JavaScript для debouncing кнопки "Сменить цель" --}}
<script>
// Проверяем, не определена ли уже функция
if (typeof handleChangeTargetSubmit === 'undefined') {
    let lastChangeTargetTime = 0;
    const CHANGE_TARGET_COOLDOWN = 1000; // 1 секунда

    function handleChangeTargetSubmit(form) {
        const currentTime = Date.now();
        const timeSinceLastChange = currentTime - lastChangeTargetTime;

        if (timeSinceLastChange < CHANGE_TARGET_COOLDOWN) {
            const remainingTime = Math.ceil((CHANGE_TARGET_COOLDOWN - timeSinceLastChange) / 1000);

            // Показываем сообщение об ошибке
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed top-4 right-4 bg-red-600 text-white px-4 py-2 rounded-md shadow-lg z-50';
            errorDiv.textContent = `Подождите ${remainingTime} сек. перед сменой цели`;
            document.body.appendChild(errorDiv);

            // Удаляем сообщение через 3 секунды
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 3000);

            return false; // Предотвращаем отправку формы
        }

        // Обновляем время последней смены цели
        lastChangeTargetTime = currentTime;

        // Блокируем кнопку и показываем индикатор загрузки
        const button = form.querySelector('button[type="submit"]');
        const buttonText = button.querySelector('.button-text');

        if (button && buttonText) {
            button.disabled = true;
            button.classList.add('opacity-50', 'cursor-not-allowed');
            buttonText.textContent = 'Смена цели...';

            // Разблокируем кнопку через 2 секунды на случай ошибки
            setTimeout(() => {
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
                buttonText.textContent = 'Сменить цель';
            }, 2000);
        }

        return true; // Разрешаем отправку формы
    }
}
</script>
