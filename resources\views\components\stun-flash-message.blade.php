@php
    // Компонент для отображения flash сообщений стана в том же стиле что и обычные flash сообщения
    $stunMessage = session('stun_error') ?? session('error');
    $isStunMessage = session('stun_error') || ($stunMessage && (
        str_contains($stunMessage, 'оглушен') ||
        str_contains($stunMessage, 'стан') ||
        str_contains($stunMessage, '⚡')
    ));
@endphp

@if($isStunMessage)
    {{-- Отображаем stun сообщение с кнопкой обновления --}}
    <div
        class="bg-gradient-to-r from-red-800/90 to-red-700/90 text-red-100 p-1.5 mx-0 my-1.5 text-xs rounded border border-red-600/50 shadow-inner shadow-black/30 mb-2 animate-fade-in">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div class="flex items-center">
                <span class="mr-1">⚡</span>
                <span>{!! $stunMessage !!}</span>
            </div>
            <button onclick="location.reload()" class="bg-gradient-to-b from-red-700 to-red-800 hover:from-red-600 hover:to-red-700
                               text-white px-3 py-1 rounded text-xs border border-red-600
                               transition-all duration-200 flex-shrink-0 self-start sm:self-auto
                               hover:shadow-md active:scale-95">
                Обновить страницу
            </button>
        </div>
    </div>
@endif