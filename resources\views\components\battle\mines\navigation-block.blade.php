@props(['isStunned' => false, 'routePrefix' => 'battle.mines.custom', 'currentLocation' => '', 'subLocations' => null])

@php
    // Получаем текущую локацию и навигационные элементы
    $currentMineLocation = \App\Models\MineLocation::where('slug', request()->route('slug'))->first();
    $navLinks = [];

    if ($currentMineLocation) {
        if ($currentMineLocation->isSubLocation()) {
            // Для подлокации: всегда показываем родительскую локацию + текущую + соседние
            $parentLocation = $currentMineLocation->parent;
            $allSiblings = $parentLocation->sublocations()
                ->where('is_active', true)
                ->orderBy('order')
                ->get();

            // Находим индекс текущей локации среди соседних
            $currentIndex = $allSiblings->search(function ($item) use ($currentMineLocation) {
                return $item->id === $currentMineLocation->id;
            });

            // Всегда показываем родительскую локацию (возможность подняться вверх)
            $navLinks[] = [
                'name' => '↑ ' . $parentLocation->name,
                'route' => route('battle.mines.custom.index', ['slug' => $parentLocation->slug]),
                'is_current' => false,
                'type' => 'parent'
            ];

            if ($currentIndex !== false) {
                // Предыдущая подлокация (если есть и это не первая)
                if ($currentIndex > 0) {
                    $prevLocation = $allSiblings[$currentIndex - 1];
                    $navLinks[] = [
                        'name' => '↑ ' . $prevLocation->name,
                        'route' => route('battle.mines.custom.index', ['slug' => $prevLocation->slug]),
                        'is_current' => false,
                        'type' => 'prev'
                    ];
                }

                // Текущая локация
                $navLinks[] = [
                    'name' => '• ' . $currentMineLocation->name . ' (текущая)',
                    'route' => route('battle.mines.custom.index', ['slug' => $currentMineLocation->slug]),
                    'is_current' => true,
                    'type' => 'current'
                ];

                // Следующая подлокация (если есть)
                if ($currentIndex < $allSiblings->count() - 1) {
                    $nextLocation = $allSiblings[$currentIndex + 1];
                    $navLinks[] = [
                        'name' => '↓ ' . $nextLocation->name,
                        'route' => route('battle.mines.custom.index', ['slug' => $nextLocation->slug]),
                        'is_current' => false,
                        'type' => 'next'
                    ];
                }
            }
        } else {
            // Для главной локации: показываем текущую и первую дочернюю
            $navLinks[] = [
                'name' => '• ' . $currentMineLocation->name . ' (текущая)',
                'route' => route('battle.mines.custom.index', ['slug' => $currentMineLocation->slug]),
                'is_current' => true,
                'type' => 'current'
            ];

            // Показываем только первую дочернюю локацию
            $firstChild = $currentMineLocation->sublocations()
                ->where('is_active', true)
                ->orderBy('order')
                ->first();

            if ($firstChild) {
                $navLinks[] = [
                    'name' => '↓ ' . $firstChild->name,
                    'route' => route('battle.mines.custom.index', ['slug' => $firstChild->slug]),
                    'is_current' => false,
                    'type' => 'next'
                ];
            }
        }
    }
@endphp

{{-- Навигация по локациям рудника --}}
@if(!empty($navLinks))
    <div class="mt-2 mb-1 bg-[#2a2722] rounded-lg border border-[#3b3629] p-2">
        <div class="text-center text-[#e4d7b0] text-xs font-medium mb-1">Навигация по рудникам</div>
        <div class="flex flex-col gap-1 text-xs">
            @foreach($navLinks as $link)
                @php
                    // Определяем стили в зависимости от типа ссылки (выравнивание по левому краю)
                    $baseClasses = 'block px-2 py-1 rounded text-left transition-colors duration-200';
                    $typeClasses = match ($link['type']) {
                        'current' => 'bg-[#6e3f35] text-[#f8eac2] border border-[#c1a96e] font-medium',
                        'parent' => 'bg-[#2f473c] text-[#d4cbb0] border border-[#3b3629] hover:bg-[#1e2e27] hover:text-[#e4d7b0]',
                        'prev' => 'bg-[#3b3629] text-[#c1a96e] border border-[#6e3f35] hover:bg-[#6e3f35] hover:text-[#e4d7b0]',
                        'next' => 'bg-[#3b3629] text-[#c1a96e] border border-[#6e3f35] hover:bg-[#6e3f35] hover:text-[#e4d7b0]',
                        default => 'bg-[#3b3629] text-[#c1a96e] border border-[#6e3f35] hover:bg-[#6e3f35] hover:text-[#e4d7b0]'
                    };
                    $stunClasses = $isStunned ? 'opacity-40 grayscale cursor-not-allowed pointer-events-none' : '';
                @endphp

                @if($link['is_current'])
                    <div class="{{ $baseClasses }} {{ $typeClasses }} {{ $stunClasses }}">
                        {{ $link['name'] }}
                    </div>
                @else
                    <a href="{{ $link['route'] }}" class="{{ $baseClasses }} {{ $typeClasses }} {{ $stunClasses }}" {{ $isStunned ? 'onclick="event.preventDefault()"' : '' }}>
                        {{ $link['name'] }}
                    </a>
                @endif
            @endforeach
        </div>
    </div>
@endif