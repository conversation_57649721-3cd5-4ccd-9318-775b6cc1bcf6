@props([
    'user' => null,
    'targetMob' => null,
    'target' => null,
    'isStunned' => false,
    'routePrefix' => 'battle.outposts',
])

{{--
    Компонент для отображения действий с выбранной целью в локациях аванпоста
    Принимает:
    - user: текущий пользователь
    - targetMob: выбранный моб (если есть)
    - target: выбранный игрок (если есть)
    - isStunned: флаг оглушения игрока
    - routePrefix: префикс маршрута для действий
--}}

@php
    // Проверяем, находится ли игрок-цель в той же локации, что и текущий пользователь
    $isTargetInLocation = false;
    $currentLocation = $user->statistics->current_location ?? null;

    if ($target && $user->current_target_type === 'player') {
        // Получаем актуальные ресурсы игрока-цели из Redis
        $targetActualResources = null;
        try {
            $targetActualResources = $target->profile->getActualResources();
        } catch (\Exception $e) {
            // Если не удается получить актуальные ресурсы, используем данные из БД
            $targetActualResources = [
                'current_hp' => $target->profile->hp ?? 0,
                'max_hp' => $target->profile->max_hp ?? 1
            ];
        }

        // Проверяем, находится ли игрок-цель в той же локации и имеет ли актуальное HP > 0
        $isTargetInLocation = $target->statistics &&
                             $target->statistics->current_location === $currentLocation &&
                             $target->profile &&
                             $targetActualResources['current_hp'] > 0; // Используем актуальное HP из Redis

        // Если цель не в локации, сбрасываем её
        if (!$isTargetInLocation && $user->current_target_type === 'player' && $user->current_target_id === $target->id) {
            // Здесь мы не можем напрямую сбросить цель, но можем отобразить сообщение
            // Фактический сброс цели будет выполнен в контроллере при следующем действии
        }
    }
@endphp

<div class="bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden mb-3">
    {{-- Заголовок блока --}}
    <div class="bg-gradient-to-b from-[#5a4d36] to-[#3a321c] border-b border-[#8c7a55] px-2 py-1.5">
        <h3 class="text-center text-[#e9d5a0] font-bold text-sm tracking-wide">Действия</h3>
    </div>

    <div class="p-3">
        @if($user->current_target_type === 'mob' && $targetMob)
            {{-- Информация о выбранном мобе --}}
            <div class="flex items-center mb-3">
                <div class="relative mr-3">
                    <img src="{{ asset($targetMob->image_path ?? 'assets/mobs/default.png') }}" alt="{{ $targetMob->name }}"
                        class="w-12 h-12 rounded-full object-cover border-2 border-[#4a9362] mob-icon-glow">

                    {{-- Индикатор уровня --}}
                    <div class="absolute -bottom-1 -right-1 bg-[#2f2d2b] border border-[#a6925e] rounded-full w-5 h-5 flex items-center justify-center">
                        <span class="text-[10px] font-bold text-[#e5b769]">{{ $targetMob->level }}</span>
                    </div>
                </div>

                <div class="flex-1">
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-semibold text-[#e5b769]">{{ $targetMob->name }}</span>
                        <span class="text-xs text-[#b0a890]">Ур. {{ $targetMob->level }}</span>
                    </div>

                    @php
                        $hpPercent = (($targetMob->current_hp ?? $targetMob->hp) / ($targetMob->max_hp ?? $targetMob->hp)) * 100;
                        $hpColor = $hpPercent > 70 ? '#4CAF50' : ($hpPercent > 35 ? '#FFC107' : '#F44336');
                    @endphp

                    <div class="w-full bg-[#1f1e1c] rounded-full h-2 mb-1">
                        <div class="h-full rounded-full" style="width: {{ $hpPercent }}%; background-color: {{ $hpColor }};"></div>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-xs text-[#b0a890]">HP: {{ $targetMob->current_hp ?? $targetMob->hp }}/{{ $targetMob->max_hp ?? $targetMob->hp }}</span>
                        <span class="text-xs text-[#b0a890]">{{ number_format($hpPercent, 0) }}%</span>
                    </div>
                </div>
            </div>

            {{-- Кнопка атаки моба --}}
            <div class="grid grid-cols-1 gap-2">
                <form id="attackForm" action="{{ route($routePrefix . '.attack_mob', request()->route('id')) }}" method="POST">
                    @csrf
                    <button id="attackButton" type="submit" {{ $isStunned ? 'disabled' : '' }}
                        class="w-full py-2 bg-gradient-to-b from-[#607d4a] to-[#3a5a2c]
                        border-2 border-[#8bae6f] rounded-md text-white text-sm font-bold shadow-md
                        {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#6b8a53] hover:to-[#44653c] active:from-[#526b3f] active:to-[#2f4a24]' }} transition-all
                        text-center">
                        {{ $isStunned ? 'Оглушен: действие невозможно' : 'Атаковать' }}
                    </button>
                </form>

                {{-- Кнопка "Сменить цель" с debouncing --}}
                <form action="{{ route($routePrefix . '.change_target', request()->route('id')) }}" method="POST"
                      onsubmit="return handleChangeTargetSubmit(this)">
                    @csrf
                    <button type="submit" {{ $isStunned ? 'disabled' : '' }}
                        id="changeTargetBtn"
                        class="w-full py-1.5 bg-gradient-to-b from-[#7a6745] to-[#5a4d36]
                        border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md
                        {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b]' }} transition-all
                        text-center">
                        <span class="button-text">{{ $isStunned ? 'Заблокировано' : 'Сменить цель' }}</span>
                    </button>
                </form>

                {{-- Кнопка обновления страницы при стане --}}
                @if ($isStunned)
                    <button
                        onclick="window.location.reload()"
                        class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                        border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                        hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                        text-center">
                        🔄 Обновить страницу
                    </button>
                @endif
            </div>
        @elseif($user->current_target_type === 'bot' && $target)
            {{-- Информация о выбранном боте --}}
            <div class="flex items-center mb-3">
                <div class="relative mr-3">
                    <img src="{{ asset($target->image_path ?? 'assets/bots/default.png') }}" alt="{{ $target->name }}"
                        class="w-12 h-12 rounded-full object-cover border-2 border-[#8a5e8a] bot-icon-glow">

                    {{-- Индикатор уровня --}}
                    <div class="absolute -bottom-1 -right-1 bg-[#2f2d2b] border border-[#a6925e] rounded-full w-5 h-5 flex items-center justify-center">
                        <span class="text-[10px] font-bold text-[#e5b769]">{{ $target->level ?? 1 }}</span>
                    </div>
                </div>

                <div class="flex-1">
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-semibold text-[#e5b769]">{{ $target->name }}</span>
                        <span class="text-xs text-[#b0a890]">Ур. {{ $target->level ?? 1 }}</span>
                    </div>

                    @php
                        $hpPercent = ($target->hp / ($target->max_hp ?? $target->hp)) * 100;
                        $hpColor = $hpPercent > 70 ? '#4CAF50' : ($hpPercent > 35 ? '#FFC107' : '#F44336');
                    @endphp

                    <div class="w-full bg-[#1f1e1c] rounded-full h-2 mb-1">
                        <div class="h-full rounded-full" style="width: {{ $hpPercent }}%; background-color: {{ $hpColor }};"></div>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-xs text-[#b0a890]">HP: {{ $target->hp }}/{{ $target->max_hp ?? $target->hp }}</span>
                        <span class="text-xs text-[#b0a890]">{{ number_format($hpPercent, 0) }}%</span>
                    </div>
                </div>
            </div>

            {{-- Кнопка атаки бота --}}
            <div class="grid grid-cols-1 gap-2">
                <form id="attackForm" action="{{ route($routePrefix . '.attack_bot', request()->route('id')) }}" method="POST">
                    @csrf
                    <button id="attackButton" type="submit" {{ $isStunned ? 'disabled' : '' }}
                        class="w-full py-2 bg-gradient-to-b from-[#8a5e8a] to-[#6a3c6a]
                        border-2 border-[#b579b5] rounded-md text-white text-sm font-bold shadow-md
                        {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#9a6a9a] hover:to-[#7a487a] active:from-[#7a527a] active:to-[#5a305a]' }} transition-all
                        text-center">
                        {{ $isStunned ? 'Оглушен: действие невозможно' : 'Атаковать' }}
                    </button>
                </form>

                {{-- Кнопка "Сменить цель" для ботов с debouncing --}}
                <form action="{{ route($routePrefix . '.change_target', request()->route('id')) }}" method="POST"
                      onsubmit="return handleChangeTargetSubmit(this)">
                    @csrf
                    <button type="submit" {{ $isStunned ? 'disabled' : '' }}
                        class="w-full py-1.5 bg-gradient-to-b from-[#7a6745] to-[#5a4d36]
                        border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md
                        {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b]' }} transition-all
                        text-center change-target-btn">
                        <span class="button-text">{{ $isStunned ? 'Заблокировано' : 'Сменить цель' }}</span>
                    </button>
                </form>

                {{-- Кнопка обновления страницы при стане --}}
                @if ($isStunned)
                    <button
                        onclick="window.location.reload()"
                        class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                        border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                        hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                        text-center">
                        🔄 Обновить страницу
                    </button>
                @endif
            </div>
        @elseif($user->current_target_type === 'player' && $target)
            @if($isTargetInLocation)
                {{-- Информация о выбранном игроке --}}
                <div class="flex items-center mb-3">
                    <div class="relative mr-3">
                        <img src="{{ asset($target->profile->avatar ?? 'assets/avatars/default.png') }}" alt="{{ $target->name }}"
                            class="w-12 h-12 rounded-full object-cover border-2 border-[#a65e5e] player-icon-glow">

                        {{-- Индикатор уровня --}}
                        <div class="absolute -bottom-1 -right-1 bg-[#2f2d2b] border border-[#a6925e] rounded-full w-5 h-5 flex items-center justify-center">
                            <span class="text-[10px] font-bold text-[#e5b769]">{{ $target->profile->level }}</span>
                        </div>
                    </div>

                    <div class="flex-1">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-sm font-semibold text-[#e5b769]">{{ $target->name }}</span>
                            <span class="text-xs text-[#b0a890]">Ур. {{ $target->profile->level }}</span>
                        </div>

                        @php
                            $hpPercent = ($targetActualResources['current_hp'] / $targetActualResources['max_hp']) * 100;
                            $hpColor = $hpPercent > 70 ? '#4CAF50' : ($hpPercent > 35 ? '#FFC107' : '#F44336');
                        @endphp

                        <div class="w-full bg-[#1f1e1c] rounded-full h-2 mb-1">
                            <div class="h-full rounded-full" style="width: {{ $hpPercent }}%; background-color: {{ $hpColor }};"></div>
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-xs text-[#b0a890]">HP: {{ $targetActualResources['current_hp'] }}/{{ $targetActualResources['max_hp'] }}</span>
                            <span class="text-xs text-[#b0a890]">{{ number_format($hpPercent, 0) }}%</span>
                        </div>
                    </div>
                </div>

                {{-- Кнопка атаки игрока --}}
                <div class="grid grid-cols-1 gap-2">
                    <form id="attackForm" action="{{ route($routePrefix . '.attack_player', request()->route('id')) }}" method="POST">
                        @csrf
                        <button id="attackButton" type="submit" {{ $isStunned ? 'disabled' : '' }}
                            class="w-full py-2 bg-gradient-to-b from-[#a65e5e] to-[#8a3c3c]
                            border-2 border-[#c27979] rounded-md text-white text-sm font-bold shadow-md
                            {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#b66a6a] hover:to-[#9a4848] active:from-[#8a5252] active:to-[#6a3030]' }} transition-all
                            text-center">
                            {{ $isStunned ? 'Оглушен: действие невозможно' : 'Атаковать' }}
                        </button>
                    </form>

                    {{-- Кнопка "Сменить цель" для игроков с debouncing --}}
                    <form action="{{ route($routePrefix . '.change_target', request()->route('id')) }}" method="POST"
                          onsubmit="return handleChangeTargetSubmit(this)">
                        @csrf
                        <button type="submit" {{ $isStunned ? 'disabled' : '' }}
                            class="w-full py-1.5 bg-gradient-to-b from-[#7a6745] to-[#5a4d36]
                            border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md
                            {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b]' }} transition-all
                            text-center change-target-btn">
                            <span class="button-text">{{ $isStunned ? 'Заблокировано' : 'Сменить цель' }}</span>
                        </button>
                    </form>

                    {{-- Кнопка обновления страницы при стане --}}
                    @if ($isStunned)
                        <button
                            onclick="window.location.reload()"
                            class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                            border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                            hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                            text-center">
                            🔄 Обновить страницу
                        </button>
                    @endif
                </div>
            @else
                {{-- Сообщение о том, что игрок покинул локацию или умер --}}
                <div class="bg-[#3b3a33] rounded-lg p-3 mb-3 text-center">
                    @php
                        // Определяем причину недоступности цели
                        $reasonMessage = 'покинул локацию';
                        if ($target->statistics && $target->statistics->current_location === $currentLocation) {
                            // Если игрок в той же локации, но недоступен, значит у него 0 HP
                            $reasonMessage = 'побежден';
                        }
                    @endphp
                    <div class="text-[#e5b769] text-sm">
                        Игрок {{ $target->name }} {{ $reasonMessage }}
                    </div>
                    <div class="text-[#7a7666] text-xs mt-1">
                        Цель автоматически сброшена
                    </div>
                </div>
            @endif
        @else
            {{-- Нет выбранной цели --}}
            <div class="text-center py-3">
                <div class="text-[#7a7666] mb-3">
                    <i class="fas fa-crosshairs text-2xl mb-2"></i>
                    <p>Цель не выбрана</p>
                </div>

                {{-- Кнопка "Случайный игрок" --}}
                <form action="{{ route($routePrefix . '.attack_any_player', request()->route('id')) }}" method="POST">
                    @csrf
                    <button type="submit" {{ $isStunned ? 'disabled' : '' }}
                        class="w-full py-2 bg-gradient-to-b from-[#7a6745] to-[#5a4d36]
                        border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-sm font-semibold shadow-md
                        {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b]' }} transition-all
                        text-center">
                        {{ $isStunned ? 'Оглушен: действие невозможно' : 'Атаковать случайную цель' }}
                    </button>
                </form>

                {{-- Кнопка обновления страницы при стане (когда нет цели) --}}
                @if ($isStunned)
                    <button
                        onclick="window.location.reload()"
                        class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                        border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                        hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                        text-center">
                        🔄 Обновить страницу
                    </button>
                @endif
            </div>
        @endif

        {{-- Кнопка "Ответить" (если игрок был атакован) --}}
        @php
            // Проверяем последнего атакующего с учетом локации
            $validLastAttacker = null;
            $validLastAttackerResources = null;
            
            if ($lastAttacker && $lastAttacker->profile && $lastAttackerResources && $lastAttackerResources['current_hp'] > 0) {
                // Проверяем активность атакующего
                if ($lastAttacker->last_activity_timestamp >= now()->subMinutes(5)->timestamp) {
                    // Используем улучшенную логику проверки локации
                    $locationService = app(\App\Services\battle\UserLocationService::class);
                    $areInSameLocation = $locationService->arePlayersInSameLocation($user, $lastAttacker);
                    
                    if ($areInSameLocation) {
                        $validLastAttacker = $lastAttacker;
                        $validLastAttackerResources = $lastAttackerResources;
                    }
                }
            }
        @endphp
        
        @if($validLastAttacker && $validLastAttackerResources)
            @php
                // Проверяем, не является ли атакующий уже целью игрока
                $isAttackerAlreadyTargeted = Auth::user()->current_target_type === 'player' && Auth::user()->current_target_id == $validLastAttacker->id;
            @endphp

            @if(!$isAttackerAlreadyTargeted)
                <div class="mt-3 pt-3 border-t border-[#514b3c]">
                    <form action="{{ route($routePrefix . '.retaliate', request()->route('id')) }}" method="POST">
                        @csrf
                        <button type="submit" {{ $isStunned ? 'disabled' : '' }}
                            class="w-full py-2 bg-gradient-to-b from-[#a65e5e] to-[#8a3c3c]
                            border-2 border-[#c27979] rounded-md text-white text-sm font-bold shadow-md
                            {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#b66a6a] hover:to-[#9a4848] active:from-[#8a5252] active:to-[#6a3030]' }} transition-all
                            text-center">
                            {{ $isStunned ? 'Оглушен: действие невозможно' : 'Ответить на атаку!' }}
                        </button>
                    </form>

                    {{-- Кнопка обновления страницы при стане (в блоке ответного удара) --}}
                    @if ($isStunned)
                        <button
                            onclick="window.location.reload()"
                            class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                            border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                            hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                            text-center">
                            🔄 Обновить страницу
                        </button>
                    @endif
                </div>
            @endif
        @endif
    </div>
</div>

<style>
    /* Пульсирующая анимация для аватара игрока */
    @keyframes playerIconPulse {
        0% {
            box-shadow: 0 0 0px rgba(166, 94, 94, 0.3);
        }

        50% {
            box-shadow: 0 0 5px 2px rgba(166, 94, 94, 0.7);
        }

        100% {
            box-shadow: 0 0 0px rgba(166, 94, 94, 0.3);
        }
    }

    .player-icon-glow {
        animation: playerIconPulse 2s infinite ease-in-out;
    }

    /* Пульсирующая анимация для аватара бота */
    @keyframes botIconPulse {
        0% {
            box-shadow: 0 0 0px rgba(138, 94, 138, 0.3);
        }

        50% {
            box-shadow: 0 0 5px 2px rgba(138, 94, 138, 0.7);
        }

        100% {
            box-shadow: 0 0 0px rgba(138, 94, 138, 0.3);
        }
    }

    .bot-icon-glow {
        animation: botIconPulse 2s infinite ease-in-out;
    }
</style>

{{-- JavaScript для debouncing кнопки "Сменить цель" --}}
<script>
let lastChangeTargetTime = 0;
const CHANGE_TARGET_COOLDOWN = 1000; // 1 секунда

function handleChangeTargetSubmit(form) {
    const currentTime = Date.now();
    const timeSinceLastChange = currentTime - lastChangeTargetTime;

    if (timeSinceLastChange < CHANGE_TARGET_COOLDOWN) {
        const remainingTime = Math.ceil((CHANGE_TARGET_COOLDOWN - timeSinceLastChange) / 1000);

        // Показываем сообщение об ошибке
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-4 right-4 bg-red-600 text-white px-4 py-2 rounded-md shadow-lg z-50';
        errorDiv.textContent = `Подождите ${remainingTime} сек. перед сменой цели`;
        document.body.appendChild(errorDiv);

        // Удаляем сообщение через 3 секунды
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 3000);

        return false; // Предотвращаем отправку формы
    }

    // Обновляем время последней смены цели
    lastChangeTargetTime = currentTime;

    // Блокируем кнопку и показываем индикатор загрузки
    const button = form.querySelector('button[type="submit"]');
    const buttonText = button.querySelector('.button-text');

    if (button && buttonText) {
        button.disabled = true;
        button.classList.add('opacity-50', 'cursor-not-allowed');
        buttonText.textContent = 'Смена цели...';

        // Разблокируем кнопку через 2 секунды на случай ошибки
        setTimeout(() => {
            button.disabled = false;
            button.classList.remove('opacity-50', 'cursor-not-allowed');
            buttonText.textContent = 'Сменить цель';
        }, 2000);
    }

    return true; // Разрешаем отправку формы
}
</script>
