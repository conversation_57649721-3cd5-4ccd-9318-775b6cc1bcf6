@props([
    'target' => null,
    'isStunned' => false,
    'userEffects' => collect(),
    'lastAttacker' => null,
    'lastAttackerResources' => null,
    'routePrefix' => null,
    'potions' => null,
    'beltPotions' => null,
])

@php
    // Автоматическое определение префикса маршрута на основе текущей локации
    if (!$routePrefix) {
        // Получаем текущую локацию пользователя
        $currentLocation = Auth::user()->statistics->current_location ?? 'Неизвестно';

        // Маппинг локаций к префиксам маршрутов
        $locationRouteMap = [
            'Эльфийская Гавань' => 'battle.outposts.elven_haven',
            'Форт Рассвета' => 'battle.outposts.dawn_fort',
            'Песчаный Оплот' => 'battle.outposts.sandy_stronghold',
            'Аванпосты' => 'battle.outposts',
        ];

        // Устанавливаем префикс маршрута на основе текущей локации
        $routePrefix = $locationRouteMap[$currentLocation] ?? 'battle.outposts.elven_haven';
    }

    // Проверяем, находится ли игрок-цель в той же локации, что и текущий пользователь
    $isTargetInLocation = false;
    $currentUser = Auth::user();
    $currentLocation = $currentUser->statistics->current_location ?? null;

    if ($target && $currentUser->current_target_type === 'player') {
        // Проверяем, находится ли игрок-цель в той же локации
        $targetCurrentHp = 0;
        if ($target->profile) {
            try {
                $targetResources = $target->profile->getActualResources();
                $targetCurrentHp = $targetResources['current_hp'];
            } catch (\Exception $e) {
                $targetCurrentHp = $target->profile->hp ?? 0;
            }
        }

        $isTargetInLocation = $target->statistics &&
                             $target->statistics->current_location === $currentLocation &&
                             $target->profile &&
                             $targetCurrentHp > 0 &&
                             $target->last_activity_timestamp >= now()->subMinutes(5)->timestamp; // Проверяем, что игрок онлайн
    }
@endphp

    // Проверяем, что префикс маршрута не заканчивается на точку
    // Это нужно для корректного формирования полного имени маршрута
    if (substr($routePrefix, -1) === '.') {
        $routePrefix = rtrim($routePrefix, '.');
    }
@endphp
{{-- Блок действий с целью --}}
<div
    class="relative mt-4 bg-cover bg-center bg-no-repeat"
    style="background-image: url('{{ asset('assets/UI/action-bg.png') }}'); min-height: 200px;">
    {{-- Заголовок блока --}}
    <div
        class="text-center py-2 text-[#e9d5a0] font-bold text-sm relative z-10">
        Действия
    </div>

    <div class="p-4 space-y-2 relative z-10">
        @if ($isStunned)
            <div class="bg-yellow-900/50 text-yellow-400 p-4 rounded-lg text-center my-4">
                ⚡ Вы оглушены и не можете действовать! ⚡
            </div>
        @else
            {{-- Универсальный блок действий с целью (моб, игрок, бот) --}}
            @if (in_array(Auth::user()->current_target_type, ['mob', 'player', 'bot']) && $target)
                {{-- Проверяем, если цель - игрок, находится ли он в локации --}}
                @if (Auth::user()->current_target_type === 'player' && !$isTargetInLocation)
                    {{-- Сообщение о том, что игрок покинул локацию --}}
                    <div class="bg-black/40 rounded-lg p-3 mb-3 text-center backdrop-blur-sm">
                        <div class="text-[#e5b769] text-sm mb-2">
                            Игрок {{ $target->name }} покинул локацию
                        </div>
                        <div class="text-[#b0a890] text-xs mb-3">
                            Цель недоступна для атаки
                        </div>
                        {{-- Кнопка "Сменить цель" --}}
                        <form action="{{ route($routePrefix . '.change_target', ['id' => request()->route('id')]) }}" method="POST">
                            @csrf
                            <button type="submit"
                                class="w-full py-1.5 bg-gradient-to-b from-[#7a6745] to-[#5a4d36]
                                border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md
                                hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b] transition-all
                                text-center">
                                Сменить цель
                            </button>
                        </form>
                    </div>
                @else
                    {{-- Обычный блок действий с целью --}}
                    <!--
                        Русский комментарий:
                        Этот блок отображает подробную информацию о текущей цели (моб, игрок или бот).
                        Включает иконку, имя, HP, эффекты, кнопку атаки и прогресс-бар.
                        Для разных типов целей меняется только маршрут формы и скрытые поля.
                    -->
                <div class="space-y-1.5">
                    {{-- Блок иконки и информации о цели --}}
                    <div
                        class="flex items-center bg-black/40 p-1.5 rounded-md backdrop-blur-sm">
                        {{-- Иконка цели --}}
                        @if (Auth::user()->current_target_type === 'mob')
                            <img src="{{ asset($target->icon ?? 'wolfIcon.png') }}" alt="{{ $target->name }}"
                                class="w-6 h-6 rounded-md">
                        @elseif(Auth::user()->current_target_type === 'player' || Auth::user()->current_target_type === 'bot')
                            <div
                                class="w-6 h-6 rounded-md bg-gradient-to-b from-[#913838] to-[#762323] border border-[#c07777] flex items-center justify-center text-white font-bold text-xs">
                                {{ Auth::user()->current_target_type === 'player' ? 'PvP' : 'PvP' }}
                            </div>
                        @endif
                        <div class="ml-1.5 flex-1">
                            {{-- Имя и HP цели --}}
                            <div class="text-[#e9d5a0] text-xs font-semibold mb-0.5 flex justify-between">
                                {{ $target->name }}
                                <span
                                    class="text-[10px] {{ Auth::user()->current_target_type === 'mob' ? 'text-[#9c8d69]' : 'text-red-400' }}">
                                    {{--
                                    Русский комментарий:
                                    Отображаем HP и максимум для цели. Для моба — свои поля, для игрока и бота используем
                                    метод getActualResources(), который рассчитывает актуальное HP на лету из Redis.
                                    --}}
                                    @if (Auth::user()->current_target_type === 'mob')
                                        {{ $target->hp }}/{{ $target->max_hp }}
                                    @elseif(Auth::user()->current_target_type === 'player')
                                        @php
                                            // Используем try-catch для корректной обработки возможных ошибок
                                            try {
                                                // Используем стандартный метод getActualResources() для единообразия
                                                $targetActualResources = $target->profile->getActualResources();
                                            } catch (\Exception $e) {
                                                // При ошибке используем значения по умолчанию
                                                $targetActualResources = [
                                                    'current_hp' =>
                                                        $target->profile->current_hp ?? ($target->profile->hp ?? 0),
                                                    'current_mp' =>
                                                        $target->profile->current_mp ?? ($target->profile->mp ?? 0),
                                                ];
                                                \Illuminate\Support\Facades\Log::error(
                                                    'Ошибка получения актуальных ресурсов цели: ' . $e->getMessage(),
                                                    [
                                                        'target_id' => $target->id,
                                                        'profile_exists' => isset($target->profile),
                                                    ],
                                                );
                                            }
                                        @endphp
                                        ❤️
                                        {{ $targetActualResources['current_hp'] }}/{{ $target->profile->max_hp ?? 100 }}
                                    @elseif(Auth::user()->current_target_type === 'bot')
                                        @php
                                            // Используем try-catch для обработки возможных ошибок
                                            try {
                                                // Используем метод getActualResources для получения актуального HP
                                                $botActualResources = $target->getActualResources();
                                            } catch (\Exception $e) {
                                                // Если произошла ошибка, используем значения по умолчанию
                                                $botActualResources = [
                                                    'current_hp' => $target->hp ?? 0,
                                                    'current_mp' => $target->mp ?? 0,
                                                ];
                                                \Illuminate\Support\Facades\Log::error(
                                                    'Ошибка получения актуальных ресурсов бота: ' . $e->getMessage(),
                                                );
                                            }
                                        @endphp
                                        ❤️
                                        {{ $botActualResources['current_hp'] }}/{{ $target->max_hp ?? 100 }}
                                    @endif
                                </span>
                            </div>
                            {{-- Полоска HP цели --}}
                            <div
                                class="flex items-center {{ Auth::user()->current_target_type === 'player' || Auth::user()->current_target_type === 'bot' ? 'mt-0.5' : '' }}">
                                <div class="w-full h-2 bg-[#1a1915] rounded border border-[#46423a] overflow-hidden">
                                    <div class="h-full"
                                        style="width:
                                            @if (Auth::user()->current_target_type === 'mob') {{ ($target->hp / $target->max_hp) * 100 }}%
                                            @elseif(Auth::user()->current_target_type === 'player')
                                                {{ ($targetActualResources['current_hp'] / ($target->profile->max_hp ?? 100)) * 100 }}%
                                            @elseif(Auth::user()->current_target_type === 'bot')
                                                {{ ($botActualResources['current_hp'] / ($target->max_hp ?? 100)) * 100 }}% @endif
                                        ; background-color:
                                            @php
$hp = 0;
                                                $max = 1;
                                                if (Auth::user()->current_target_type === 'mob') {
                                                    $hp = $target->hp;
                                                    $max = $target->max_hp;
                                                } elseif (Auth::user()->current_target_type === 'player') {
                                                    $hp = $targetActualResources['current_hp'];
                                                    $max = $target->profile->max_hp ?? 100;
                                                } elseif (Auth::user()->current_target_type === 'bot') {
                                                    $hp = $botActualResources['current_hp'];
                                                    $max = $target->max_hp ?? 100;
                                                } @endphp
                                            {{ $hp > $max * 0.7 ? '#4CAF50' : ($hp > $max * 0.3 ? '#FFC107' : '#F44336') }};">
                                    </div>
                                </div>
                            </div>
                            {{-- Эффекты на цели --}}
                            @php
                                // Русский комментарий:
                                // Получаем эффекты для цели в зависимости от типа
                                if (Auth::user()->current_target_type === 'mob') {
                                    $effects = \App\Models\ActiveEffect::where('target_type', 'mob')
                                        ->where('target_id', $target->id)
                                        ->whereRaw('EXTRACT(EPOCH FROM (NOW() - created_at)) < duration')
                                        ->with('skill')
                                        ->get();
                                } elseif (Auth::user()->current_target_type === 'player') {
                                    $effects = \App\Models\ActiveEffect::where('target_type', 'App\\Models\\User')
                                        ->where('target_id', $target->id)
                                        ->whereRaw('EXTRACT(EPOCH FROM (NOW() - created_at)) < duration')
                                        ->with('skill')
                                        ->get();
                                } elseif (Auth::user()->current_target_type === 'bot') {
                                    $effects = \App\Models\ActiveEffect::where('target_type', 'bot')
                                        ->where('target_id', $target->id)
                                        ->whereRaw('EXTRACT(EPOCH FROM (NOW() - created_at)) < duration')
                                        ->with('skill')
                                        ->get();
                                } else {
                                    $effects = collect();
                                }
                            @endphp
                            @if ($effects->isNotEmpty())
                                <div class="flex flex-wrap gap-0.5 mt-1">
                                    @foreach ($effects as $effect)
                                        <div class="relative group">
                                            @if($effect->skill)
                                                <img src="{{ asset($effect->skill->icon ?? 'assets/default_effect.png') }}"
                                                    alt="{{ $effect->skill->name ?? 'Неизвестный эффект' }}"
                                                    class="w-3.5 h-3.5 rounded-full {{ $effect->isStunEffect() ? 'opacity-80 ring-1 ring-yellow-400/60' : ($effect->skill->type === 'debuff' ? 'ring-1 ring-red-400/60' : 'ring-1 ring-green-400/60') }}" />
                                            @else
                                                <img src="{{ asset('assets/default_effect.png') }}"
                                                    alt="Неизвестный эффект"
                                                    class="w-3.5 h-3.5 rounded-full ring-1 ring-gray-400/60" />
                                            @endif
                                            <div
                                                class="absolute bottom-full right-0 mb-1 hidden group-hover:block z-50">
                                                <div
                                                    class="bg-black/90 text-white text-[10px] px-1.5 py-0.5 rounded whitespace-nowrap">
                                                    {{ $effect->skill ? $effect->skill->name : 'Неизвестный эффект' }}
                                                    ({{ round($effect->remaining_duration) }}с)
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>
                    {{-- Кнопка атаки цели --}}
                    <form id="attackForm"
                        action="@if (Auth::user()->current_target_type === 'mob') {{ route($routePrefix . '.attack', ['id' => request()->route('id')]) }}
                        @elseif(Auth::user()->current_target_type === 'player' || Auth::user()->current_target_type === 'bot')
                            {{ route($routePrefix . '.attack_any_player', ['id' => request()->route('id')]) }} @endif"
                        method="POST">
                        @csrf
                        {{-- Для бота и игрока нужны скрытые поля --}}
                        @if (Auth::user()->current_target_type === 'bot')
                            <input type="hidden" name="target_type" value="bot">
                            <input type="hidden" name="target_id" value="{{ $target->id }}">
                        @elseif(Auth::user()->current_target_type === 'player')
                            <input type="hidden" name="target_type" value="user">
                            <input type="hidden" name="target_id" value="{{ $target->id }}">
                        @endif
                        <button id="attackButton" type="submit"
                            class="w-full py-2
                                @if (Auth::user()->current_target_type === 'mob') bg-gradient-to-b from-[#913838] to-[#762323] border-2 border-[#c07777]
                                @else
                                bg-gradient-to-b from-[#913838] to-[#762323] border-2 border-[#c07777] @endif
                                rounded-md text-white text-sm font-bold shadow-md
                                hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e] transition-all text-center">
                            {{--
                            Русский комментарий:
                            Текст кнопки одинаков для всех типов целей
                            --}}
                            @if (Auth::user()->current_target_type === 'mob')
                                Атаковать
                            @elseif(Auth::user()->current_target_type === 'player')
                                Атаковать
                            @else
                                Атаковать
                            @endif
                        </button>
                        {{-- Полоса заряда атаки --}}
                        <div class="w-full h-2 bg-[#222019] rounded-sm mt-1.5 border border-[#46423a] overflow-hidden">
                            <div id="progressBar" class="h-full bg-[#F44336] rounded-sm transition-all"
                                style="width: 0%;">
                            </div>
                        </div>
                    </form>
                    {{-- Кнопка смены цели (когда выбран моб, бот или игрок) с debouncing --}}
                    @if (Auth::user()->current_target_type === 'mob' || Auth::user()->current_target_type === 'bot' || Auth::user()->current_target_type === 'player')
                        <form action="{{ route($routePrefix . '.change_target', ['id' => request()->route('id')]) }}" method="POST"
                              onsubmit="return handleChangeTargetSubmit(this)">
                            @csrf
                            <button type="submit" {{ $isStunned ? 'disabled' : '' }}
                                class="w-full py-1.5 bg-gradient-to-b from-[#7a6745] to-[#5a4d36] border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md
                                {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b]' }} transition-all text-center change-target-btn">
                                <span class="button-text">{{ $isStunned ? 'Заблокировано' : 'Сменить цель' }}</span>
                            </button>
                        </form>
                    @endif

                    {{-- Кнопка обновления страницы при стане --}}
                    @if ($isStunned)
                        <button
                            onclick="window.location.reload()"
                            class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                            border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                            hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                            text-center">
                            🔄 Обновить страницу
                        </button>
                    @endif
                </div>
                @endif
            @else
                {{-- Нет цели - показываем заголовок блока и кнопку выбора случайного игрока --}}
                <div
                    class="text-center text-[#e9d5a0] p-2 bg-black/40 rounded-md backdrop-blur-sm mb-2">
                    Выберите цель для атаки
                </div>
                <form action="{{ route($routePrefix . '.attack_any_player', ['id' => request()->route('id')]) }}" method="POST">
                    @csrf
                    <button type="submit" {{ $isStunned ? 'disabled' : '' }}
                        class="w-full py-2 bg-gradient-to-b from-[#7a6745] to-[#5a4d36] border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md
                        {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b]' }} transition-all text-center">
                        {{ $isStunned ? 'Заблокировано' : 'Бить любого' }}
                    </button>
                </form>

                {{-- Кнопка обновления страницы при стане (когда нет цели) --}}
                @if ($isStunned)
                    <button
                        onclick="window.location.reload()"
                        class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                        border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                        hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                        text-center">
                        🔄 Обновить страницу
                    </button>
                @endif
            @endif

            {{-- Блок быстрого использования зелий с пояса --}}
            <div class="mt-3 mb-2">
                <x-user.quick-potion-bar class="flex justify-center items-center" />
            </div>

            {{-- Дополнительные кнопки (Ответный удар) --}}
            <div class="pt-1 space-y-1.5">
                @php
                    $currentUser = Auth::user();
                    $currentLocation = $currentUser->statistics->current_location ?? 'Неизвестно';
                    $lastAttacker = $currentUser->last_attacker_id
                        ? \App\Models\User::where('id', $currentUser->last_attacker_id)
                            ->whereHas('statistics', function ($query) use ($currentLocation) {
                                $query->where('current_location', $currentLocation);
                            })
                            ->with('profile', 'statistics')
                            ->first()
                        : null;

                    // Проверяем, не выбран ли уже атакующий в качестве цели
                    $isAttackerAlreadyTargeted =
                        $lastAttacker &&
                        $currentUser->current_target_type === 'player' &&
                        $currentUser->current_target_id == $lastAttacker->id;
                @endphp

                @php
                    // Получаем актуальные ресурсы атакующего через getActualResources()
                    $actualLastAttackerResources = null;
                    if ($lastAttacker && $lastAttacker->profile) {
                        try {
                            $actualLastAttackerResources = $lastAttacker->profile->getActualResources();
                        } catch (\Exception $e) {
                            $actualLastAttackerResources = [
                                'current_hp' => $lastAttacker->profile->hp ?? 0,
                                'current_mp' => $lastAttacker->profile->mp ?? 0,
                            ];
                            \Illuminate\Support\Facades\Log::error(
                                'Ошибка получения актуальных ресурсов атакующего: ' . $e->getMessage(),
                            );
                        }
                    }
                @endphp

                @if (
                    $lastAttacker &&
                        $lastAttacker->profile &&
                        $actualLastAttackerResources &&
                        $actualLastAttackerResources['current_hp'] > 0 &&
                        !$isAttackerAlreadyTargeted)
                    @php
                        // Рассчитываем процент HP для полоски
                        $hpPercent = ($actualLastAttackerResources['current_hp'] / $lastAttacker->profile->max_hp) * 100;
                        // Определяем цвет индикатора в зависимости от % здоровья
                        $hpColor =
                            $hpPercent > 70 ? 'bg-green-600' : ($hpPercent > 30 ? 'bg-yellow-500' : 'bg-red-600');
                    @endphp

                    <form action="{{ route($routePrefix . '.retaliate', ['id' => request()->route('id')]) }}" method="POST">
                        @csrf
                        <button type="submit"
                            class="w-full py-2 bg-gradient-to-b from-[#913838] to-[#762323]
                                border-2 border-[#c07777] rounded-md text-white text-xs font-semibold shadow-md
                                hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e]
                                transition-all duration-300 text-center group relative overflow-hidden">

                            {{-- Фоновая анимация при наведении --}}
                            <span
                                class="absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b6b40] to-transparent opacity-0 group-hover:opacity-100 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>

                            <div class="flex items-center justify-center relative z-10">
                                {{-- Иконка атаки --}}
                                <span class="mr-1 text-red-300">⚔️</span>

                                {{-- Текст кнопки --}}
                                <span class="font-bold">Бить в ответ</span>

                                {{-- Имя и HP противника в стильном контейнере --}}
                                <div
                                    class="ml-2 flex items-center bg-[#6b2c2c] px-2 py-0.5 rounded border border-[#c07777] shadow-inner">
                                    <span class="text-[10px] text-yellow-200 mr-1">{{ $lastAttacker->name }}</span>

                                    {{-- Мини-полоска HP --}}
                                    <div class="w-14 h-1.5 bg-[#421a1a] rounded-full overflow-hidden flex-shrink-0">
                                        <div class="{{ $hpColor }} h-full" style="width: {{ $hpPercent }}%;">
                                        </div>
                                    </div>

                                    {{-- Числовое значение HP --}}
                                    <span class="text-[9px] text-gray-300 ml-1">
                                        {{ $actualLastAttackerResources['current_hp'] }}<span
                                            class="text-gray-500">/{{ $lastAttacker->profile->max_hp }}</span>
                                    </span>
                                </div>
                            </div>
                        </button>
                    </form>
                @endif

                {{-- Кнопка обновления страницы при стане (в блоке ответного удара) --}}
                @if ($isStunned)
                    <button
                        onclick="window.location.reload()"
                        class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                        border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                        hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                        text-center">
                        🔄 Обновить страницу
                    </button>
                @endif
            </div>
        @endif
    </div>
</div>

{{-- JavaScript для debouncing кнопки "Сменить цель" --}}
<script>
// Проверяем, не определена ли уже функция
if (typeof handleChangeTargetSubmit === 'undefined') {
    let lastChangeTargetTime = 0;
    const CHANGE_TARGET_COOLDOWN = 1000; // 1 секунда

    function handleChangeTargetSubmit(form) {
        const currentTime = Date.now();
        const timeSinceLastChange = currentTime - lastChangeTargetTime;

        if (timeSinceLastChange < CHANGE_TARGET_COOLDOWN) {
            const remainingTime = Math.ceil((CHANGE_TARGET_COOLDOWN - timeSinceLastChange) / 1000);

            // Показываем сообщение об ошибке
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed top-4 right-4 bg-red-600 text-white px-4 py-2 rounded-md shadow-lg z-50';
            errorDiv.textContent = `Подождите ${remainingTime} сек. перед сменой цели`;
            document.body.appendChild(errorDiv);

            // Удаляем сообщение через 3 секунды
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 3000);

            return false; // Предотвращаем отправку формы
        }

        // Обновляем время последней смены цели
        lastChangeTargetTime = currentTime;

        // Блокируем кнопку и показываем индикатор загрузки
        const button = form.querySelector('button[type="submit"]');
        const buttonText = button.querySelector('.button-text');

        if (button && buttonText) {
            button.disabled = true;
            button.classList.add('opacity-50', 'cursor-not-allowed');
            buttonText.textContent = 'Смена цели...';

            // Разблокируем кнопку через 2 секунды на случай ошибки
            setTimeout(() => {
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
                buttonText.textContent = 'Сменить цель';
            }, 2000);
        }

        return true; // Разрешаем отправку формы
    }
}
</script>
