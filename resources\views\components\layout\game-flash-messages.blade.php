{{--
Компонент флеш-сообщений для игровых страниц
Отображает сообщения об успехе, ошибках, предупреждениях и информации
--}}

@php
    // Определяем стили для разных типов сообщений
    $messageTypes = [
        'success' => [
            'bg' => 'bg-gradient-to-r from-green-800/90 to-green-700/90',
            'text' => 'text-green-100',
            'border' => 'border-green-600/50',
            'icon' => '✓'
        ],
        'error' => [
            'bg' => 'bg-gradient-to-r from-red-800/90 to-red-700/90',
            'text' => 'text-red-100',
            'border' => 'border-red-600/50',
            'icon' => '✗'
        ],
        'warning' => [
            'bg' => 'bg-gradient-to-r from-yellow-800/90 to-yellow-700/90',
            'text' => 'text-yellow-100',
            'border' => 'border-yellow-600/50',
            'icon' => '⚠'
        ],
        'info' => [
            'bg' => 'bg-gradient-to-r from-[#3b3629] to-[#2a2722]',
            'text' => 'text-[#e4d7b0]',
            'border' => 'border-[#6e3f35]',
            'icon' => '<img src="/assets/notification/info.png" alt="info" class="inline-block w-4 h-4 mr-1">'

        ]
    ];
@endphp

<div class="game-flash-messages relative z-40">
    {{-- Специальные сообщения стана с кнопкой обновления --}}
    <x-stun-flash-message />

    {{-- Сообщения об успехе --}}
    @if (session('success'))
        <div
            class="{{ $messageTypes['success']['bg'] }} {{ $messageTypes['success']['text'] }} p-1.5 mx-2 my-1.5 text-xs rounded border {{ $messageTypes['success']['border'] }} shadow-inner shadow-black/30 mb-2 animate-fade-in">
            <span class="mr-1">{{ $messageTypes['success']['icon'] }}</span>
            {!! session('success') !!}
        </div>
    @endif

    {{-- Сообщения об ошибке (исключая stun сообщения) --}}
    @if (session('error') && !session('stun_error'))
        @php
            $errorMessage = session('error');
            $isStunError = str_contains($errorMessage, 'оглушен') ||
                str_contains($errorMessage, 'стан') ||
                str_contains($errorMessage, '⚡');
        @endphp
        @if (!$isStunError)
            <div
                class="{{ $messageTypes['error']['bg'] }} {{ $messageTypes['error']['text'] }} p-1.5 mx-0 my-1.5 text-xs rounded border {{ $messageTypes['error']['border'] }} shadow-inner shadow-black/30 mb-2 animate-fade-in">
                <span class="mr-1">{{ $messageTypes['error']['icon'] }}</span>
                {!! session('error') !!}
            </div>
        @endif
    @endif

    {{-- Сообщения-предупреждения --}}
    @if (session('warning'))
        <div
            class="{{ $messageTypes['warning']['bg'] }} {{ $messageTypes['warning']['text'] }} p-1.5 mx-2 my-1.5 text-xs rounded border {{ $messageTypes['warning']['border'] }} shadow-inner shadow-black/30 mb-2 animate-fade-in">
            <span class="mr-1">{{ $messageTypes['warning']['icon'] }}</span>
            {!! session('warning') !!}
        </div>
    @endif

    {{-- Информационные сообщения --}}
    @if (session('info'))
        <div
            class="{{ $messageTypes['info']['bg'] }} {{ $messageTypes['info']['text'] }} p-1.5 mx-2 my-1.5 text-xs rounded border {{ $messageTypes['info']['border'] }} shadow-inner shadow-black/30 mb-2 animate-fade-in">
            <span class="mr-0">{!! $messageTypes['info']['icon'] !!}</span>
            {!! session('info') !!}
        </div>
    @endif

    {{-- Специальное сообщение приветствия --}}
    @if (session('welcome_message'))
        <div
            class="bg-[#3b3a33] text-[#e5b769] p-2 rounded mb-2 mt-2 border border-[#a6925e] shadow-md animate-fade-in text-center">
            {{ session('welcome_message') }}
        </div>
    @endif

    {{-- Сообщение о блокировке доступа в подземелье --}}
    @if (session('dungeon_access_blocked'))
        @php
            $blockData = session('dungeon_access_blocked');
        @endphp
        <div
            class="bg-gradient-to-r from-[#6c4539] to-[#2a1b12] border border-[#6c4539] rounded-lg p-3 mx-2 my-2 animate-fade-in relative z-50">
            <div class="flex items-center space-x-2 mb-2">
                <span class="text-[#f28b38] text-lg">⚠️</span>
                <span class="text-[#fceac4] font-semibold">Вы находитесь в подземелье</span>
            </div>
            <p class="text-[#fceac4] text-sm leading-relaxed mb-3">
                {{ $blockData['message'] }}
            </p>
            <p class="text-[#a6925e] text-xs mb-3">
                Если вы покидаете подземелье, то также покидаете группу.
            </p>

            {{-- Убрано дублирование кнопки "Покинуть подземелье" - теперь она только под заголовком --}}
            <div class="flex justify-center">
                {{-- Кнопка "Отмена" (закрыть сообщение) --}}
                <button onclick="this.parentElement.parentElement.style.display='none'"
                    class="bg-gradient-to-br from-[#3e5c48] to-[#243c2f] text-[#fceac4] font-semibold py-2 px-4 rounded border border-[#3e5c48] hover:from-[#4a6b54] hover:to-[#2d4738] transition-all duration-300 text-sm">
                    ❌ Закрыть уведомление
                </button>
            </div>
        </div>
    @endif

    {{-- Сообщение о подтверждении выхода из подземелья --}}
    @if (session('dungeon_leave_confirmation'))
        @php
            $confirmData = session('dungeon_leave_confirmation');
        @endphp
        <div id="dungeon-leave-confirmation"
            class="bg-[#1a1814] border border-[#3b3629] rounded-md p-2 mx-1 my-0 animate-fade-in shadow-md relative z-50"
            style="box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.85), inset 0 1px 0 rgba(255, 255, 255, 0.02); font-size: 13px;">

            <div class="flex items-center space-x-1 mb-1">
                <span class="text-[#c1a96e] text-sm" style="filter: drop-shadow(0 0 4px rgba(193, 169, 110, 0.4));"></span>
                <span class="text-[#e4d7b0] font-semibold text-sm" style="text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);">
                    Покинуть подземелье?
                </span>
            </div>

            <p class="text-[#d4cbb0] text-xs leading-tight mb-1">
                {{ $confirmData['message'] }}
            </p>
            <p class="text-[#998d66] text-[10px] italic mb-2">
                При выходе из подземелья вы также покинете группу.
            </p>

            <div class="flex space-x-1">
                {{-- Кнопка "Покинуть" --}}
                <form action="{{ route('dungeons.force-leave') }}" method="POST" class="flex-1">
                    @csrf
                    <input type="hidden" name="intended_url" value="{{ $confirmData['requested_url'] ?? route('home') }}">
                    <button type="submit"
                        class="w-full bg-gradient-to-br from-[#59372d] to-[#3c221b] text-[#f8eac2] font-medium py-1 px-2 rounded border border-[#6e3f35] hover:from-[#704135] hover:to-[#4a2b22] transition-all duration-200 text-xs shadow-sm hover:shadow-md"
                        style="text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);">
                        ✔ Покинуть
                    </button>
                </form>

                {{-- Кнопка "Остаться" --}}
                <button onclick="document.getElementById('dungeon-leave-confirmation').style.display='none'"
                    class="flex-1 bg-gradient-to-br from-[#2f473c] to-[#1e2e27] text-[#f8eac2] font-medium py-1 px-2 rounded border border-[#3e5c48] hover:from-[#3c5c48] hover:to-[#243b2f] transition-all duration-200 text-xs shadow-sm hover:shadow-md"
                    style="text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);">
                    ✖ Остаться
                </button>
            </div>
        </div>


    @endif
</div>

{{-- Стили для анимации --}}
<style>
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fadeIn 0.3s ease-out forwards;
    }

    /* Обеспечиваем правильное позиционирование flash-сообщений */
    .game-flash-messages {
        position: relative;
        z-index: 40;
    }

    /* Специальные стили для критически важных сообщений */
    .game-flash-messages>div[class*="dungeon_leave_confirmation"],
    .game-flash-messages>div[class*="dungeon_access_blocked"] {
        position: relative;
        z-index: 50;
        margin-bottom: 8px;
    }
</style>