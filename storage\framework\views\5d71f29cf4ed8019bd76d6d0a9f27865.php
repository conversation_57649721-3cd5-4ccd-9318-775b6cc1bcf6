<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?> 

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Эльфийская Гавань - <?php echo e(Auth::check() ? Auth::user()->name : 'Гость'); ?></title>

    
    <?php if (isset($component)) { $__componentOriginal37ce48162d92df5270eb895c968a5212 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal37ce48162d92df5270eb895c968a5212 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.secure-assets','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('secure-assets'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal37ce48162d92df5270eb895c968a5212)): ?>
<?php $attributes = $__attributesOriginal37ce48162d92df5270eb895c968a5212; ?>
<?php unset($__attributesOriginal37ce48162d92df5270eb895c968a5212); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal37ce48162d92df5270eb895c968a5212)): ?>
<?php $component = $__componentOriginal37ce48162d92df5270eb895c968a5212; ?>
<?php unset($__componentOriginal37ce48162d92df5270eb895c968a5212); ?>
<?php endif; ?>

    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/css/battle-animations.css']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => ['hasUnreadMessages' => $hasUnreadMessages ?? false,'unreadMessagesCount' => $unreadMessagesCount ?? 0,'hasBrokenItems' => $hasBrokenItems ?? false,'brokenItemsCount' => $brokenItemsCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasUnreadMessages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasUnreadMessages ?? false),'unreadMessagesCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($unreadMessagesCount ?? 0),'hasBrokenItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasBrokenItems ?? false),'brokenItemsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($brokenItemsCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal85b5c6510a4667d62a225031f53f7a0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.currency-display','data' => ['userProfile' => $userProfile,'experienceProgress' => $experienceProgress ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.currency-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile),'experienceProgress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($experienceProgress ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $attributes = $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $component = $__componentOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>



        
        <div class="text-center flex justify-center space-x-1">
            <?php if(session('welcome_message')): ?>
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-2 mt-2 w-full">
                    <?php echo e(session('welcome_message')); ?>

                </div>
            <?php endif; ?>
        </div>

        
        <div class="mb-2">
            
            <?php if (isset($component)) { $__componentOriginal5e903ecf51adf2fc13a9a2695cfb0e86 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5e903ecf51adf2fc13a9a2695cfb0e86 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.active-effects','data' => ['userEffects' => $userEffects]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.active-effects'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userEffects' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userEffects)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5e903ecf51adf2fc13a9a2695cfb0e86)): ?>
<?php $attributes = $__attributesOriginal5e903ecf51adf2fc13a9a2695cfb0e86; ?>
<?php unset($__attributesOriginal5e903ecf51adf2fc13a9a2695cfb0e86); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5e903ecf51adf2fc13a9a2695cfb0e86)): ?>
<?php $component = $__componentOriginal5e903ecf51adf2fc13a9a2695cfb0e86; ?>
<?php unset($__componentOriginal5e903ecf51adf2fc13a9a2695cfb0e86); ?>
<?php endif; ?>

            
            <?php
                $isStunned = $userEffects->contains(function ($effect) {
                    return $effect->skill_id == 14 && $effect->isActive();
                });
            ?>

            
            <?php if (isset($component)) { $__componentOriginal1cd0b32bf85b74bb4544df7dc59961ce = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1cd0b32bf85b74bb4544df7dc59961ce = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.mines-header','data' => ['breadcrumbs' => $breadcrumbs,'title' => $locationTitle ?? $locationName ?? 'Рудник']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.mines-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs),'title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($locationTitle ?? $locationName ?? 'Рудник')]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1cd0b32bf85b74bb4544df7dc59961ce)): ?>
<?php $attributes = $__attributesOriginal1cd0b32bf85b74bb4544df7dc59961ce; ?>
<?php unset($__attributesOriginal1cd0b32bf85b74bb4544df7dc59961ce); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1cd0b32bf85b74bb4544df7dc59961ce)): ?>
<?php $component = $__componentOriginal1cd0b32bf85b74bb4544df7dc59961ce; ?>
<?php unset($__componentOriginal1cd0b32bf85b74bb4544df7dc59961ce); ?>
<?php endif; ?>
        </div>
        
        <?php if (isset($component)) { $__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.game-flash-messages','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('game-flash-messages'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6)): ?>
<?php $attributes = $__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6; ?>
<?php unset($__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6)): ?>
<?php $component = $__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6; ?>
<?php unset($__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6); ?>
<?php endif; ?>
        
        <?php echo $__env->yieldContent('content'); ?>

        
        <div class="mt-2 mb-2">
            <?php if (isset($component)) { $__componentOriginaleedfaed7b075f91ddad1da97aa706e0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleedfaed7b075f91ddad1da97aa706e0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user.quick-potion-bar','data' => ['class' => 'flex justify-center items-center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('user.quick-potion-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'flex justify-center items-center']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleedfaed7b075f91ddad1da97aa706e0d)): ?>
<?php $attributes = $__attributesOriginaleedfaed7b075f91ddad1da97aa706e0d; ?>
<?php unset($__attributesOriginaleedfaed7b075f91ddad1da97aa706e0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleedfaed7b075f91ddad1da97aa706e0d)): ?>
<?php $component = $__componentOriginaleedfaed7b075f91ddad1da97aa706e0d; ?>
<?php unset($__componentOriginaleedfaed7b075f91ddad1da97aa706e0d); ?>
<?php endif; ?>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal4923a1cb42f469880881d13f1dd35a84 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4923a1cb42f469880881d13f1dd35a84 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.skills-panel','data' => ['routePrefix' => ''.e($routePrefix ?? 'battle.outposts.elven_haven').'','isStunned' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.skills-panel'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['routePrefix' => ''.e($routePrefix ?? 'battle.outposts.elven_haven').'','isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4923a1cb42f469880881d13f1dd35a84)): ?>
<?php $attributes = $__attributesOriginal4923a1cb42f469880881d13f1dd35a84; ?>
<?php unset($__attributesOriginal4923a1cb42f469880881d13f1dd35a84); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4923a1cb42f469880881d13f1dd35a84)): ?>
<?php $component = $__componentOriginal4923a1cb42f469880881d13f1dd35a84; ?>
<?php unset($__componentOriginal4923a1cb42f469880881d13f1dd35a84); ?>
<?php endif; ?>

        
        <?php if(request()->is('battle/mines/*') && isset($subLocations)): ?>
            <?php if (isset($component)) { $__componentOriginal342df68f17469da7d3465e61ca5b2e20 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal342df68f17469da7d3465e61ca5b2e20 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.mines.navigation-block','data' => ['isStunned' => $isStunned ?? false,'routePrefix' => 'battle.mines.custom','currentLocation' => ''.e(request()->route('slug') ?? 'default').'','subLocations' => $subLocations ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.mines.navigation-block'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned ?? false),'routePrefix' => 'battle.mines.custom','currentLocation' => ''.e(request()->route('slug') ?? 'default').'','subLocations' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($subLocations ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal342df68f17469da7d3465e61ca5b2e20)): ?>
<?php $attributes = $__attributesOriginal342df68f17469da7d3465e61ca5b2e20; ?>
<?php unset($__attributesOriginal342df68f17469da7d3465e61ca5b2e20); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal342df68f17469da7d3465e61ca5b2e20)): ?>
<?php $component = $__componentOriginal342df68f17469da7d3465e61ca5b2e20; ?>
<?php unset($__componentOriginal342df68f17469da7d3465e61ca5b2e20); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal3f9d36c673437b9c6830ab5b64ad67c9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3f9d36c673437b9c6830ab5b64ad67c9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.battle-logs','data' => ['battleLogs' => $battleLogs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.battle-logs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['battleLogs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($battleLogs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3f9d36c673437b9c6830ab5b64ad67c9)): ?>
<?php $attributes = $__attributesOriginal3f9d36c673437b9c6830ab5b64ad67c9; ?>
<?php unset($__attributesOriginal3f9d36c673437b9c6830ab5b64ad67c9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3f9d36c673437b9c6830ab5b64ad67c9)): ?>
<?php $component = $__componentOriginal3f9d36c673437b9c6830ab5b64ad67c9; ?>
<?php unset($__componentOriginal3f9d36c673437b9c6830ab5b64ad67c9); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal9e67914025cd6dd9a8c10d111e91463c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.navigation-buttons','data' => ['isDisabled' => $isStunned ?? false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.navigation-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['isDisabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned ?? false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $attributes = $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $component = $__componentOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
    </div>

    
    <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>



    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/app.js', 'resources/js/global/csrf.js', 'resources/js/attackLimiter.js', 'resources/js/layout/footer-counters.js', 'resources/js/layout/server-time.js']); ?>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/layouts/mine.blade.php ENDPATH**/ ?>